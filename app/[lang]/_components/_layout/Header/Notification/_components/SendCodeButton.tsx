import { useRequestBindEmailMutation } from '__generated__/types.app';
import { useCallback, useEffect, useState } from 'react';
import { Button } from 'react-aria-components';
import { useFormContext } from 'react-hook-form';
import { ConnectEmailFormState } from './ConnectEmailModal';

const resendInterval = 60;

type SendCodeButtonProp = {
  disabled?: boolean;
};

export function SendCodeButton({ disabled }: SendCodeButtonProp) {
  const { formState, getValues, setError } = useFormContext<ConnectEmailFormState>();
  const [{ fetching }, requestBindEmail] = useRequestBindEmailMutation();
  const [timeLeft, setTimeLeft] = useState(0);
  const [codeSent, setCodeSent] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (timeLeft > 0) {
      timer = setTimeout(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [timeLeft]);

  const handleSendCode = useCallback(async () => {
    if (formState.errors.email) {
      setError('email', { type: 'custom', message: formState.errors.email.message });
      return;
    }

    setTimeLeft(resendInterval);
    setCodeSent(true);

    const { data, error } = await requestBindEmail({ email: getValues('email') });

    if (!data?.requestBindEmailOtp || error) {
      setError('email', {
        type: 'custom',
        message:
          error?.networkError?.message ||
          error?.graphQLErrors[0]?.message ||
          'Unable to send verification code. Please try again.',
      });
    }
  }, [formState.errors.email, getValues, requestBindEmail, setError]);

  return (
    formState.dirtyFields.email && (
      <Button
        className="text-sm font-medium text-text-6 underline enabled:hover:text-text-1 disabled:no-underline"
        onPress={handleSendCode}
        isDisabled={disabled || fetching || timeLeft > 0}
      >
        {codeSent ? `Resend${timeLeft > 0 ? ` (${timeLeft}S)` : ''}` : 'Send Code'}
      </Button>
    )
  );
}
