import { Button } from '@/_components/Button';
import { Modal } from '@/_components/Modal';
import { useTranslation } from '@/_services/i18n/client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useBindEmailMutation } from '__generated__/types.app';
import clsx from 'clsx';
import { useCallback, useState } from 'react';
import { FieldError, Form, Input, Label, TextField } from 'react-aria-components';
import { Controller, FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';
import { SendCodeButton } from './SendCodeButton';

type ConnectEmailModalProp = {
  onConnectSuccess: () => void;
};

const schema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  verificationCode: z.string().min(1, 'Please enter the verification code'),
});
export type ConnectEmailFormState = z.infer<typeof schema>;

const DEFAULT_STATE: ConnectEmailFormState = {
  email: '',
  verificationCode: '',
};

export function ConnectEmailModal({ onConnectSuccess }: ConnectEmailModalProp) {
  const { t } = useTranslation();
  const [{ fetching }, bindEmail] = useBindEmailMutation();
  const [show, setShow] = useState(false);
  const methods = useForm({
    defaultValues: DEFAULT_STATE,
    resolver: zodResolver(schema),
    mode: 'onChange',
  });
  const {
    formState: { isValid },
    setError,
    handleSubmit,
  } = methods;

  const onSubmit = useCallback<SubmitHandler<ConnectEmailFormState>>(
    async ({ email, verificationCode }) => {
      const { data, error } = await bindEmail({ email, verificationCode });
      if (!data?.bindEmail || error) {
        setError('verificationCode', {
          type: 'custom',
          message: 'The verification code is not correct.',
        });
      } else {
        onConnectSuccess();
        setShow(false);
      }
    },
    [bindEmail, onConnectSuccess, setError],
  );

  return (
    <>
      <Button
        variant="contained"
        className="w-[6.25rem] py-[5px] text-sm font-medium"
        onClick={() => setShow(true)}
      >
        {`${t('connect')}`}
      </Button>
      <Modal title="Connect to email" show={show} onClose={() => setShow(false)} topMargin="mt-1">
        <FormProvider {...methods}>
          <Form
            className="w-[33.75rem] px-[3.125rem] pb-[1.875rem] pt-6 text-sm text-text-2"
            onSubmit={handleSubmit(onSubmit)}
          >
            <Controller<ConnectEmailFormState>
              name="email"
              render={({ field: { ref, disabled, ...field }, fieldState: { invalid, error } }) => (
                <TextField
                  {...field}
                  className="flex flex-col"
                  isDisabled={disabled}
                  isInvalid={invalid}
                  type="email"
                  isRequired
                  isReadOnly={fetching}
                >
                  <Label className="mb-[5px] font-semibold">Email Address</Label>
                  <Input
                    ref={ref}
                    className={clsx(
                      'rounded-sm bg-background-7 px-4 py-2.5 font-medium outline-none',
                      'border border-background-7 read-only:border-background-2',
                      'hover:border-text-4 focus:border-text-4',
                      'read-only:bg-background-2 read-only:text-text-6',
                    )}
                  />
                  {error?.type === 'custom' && (
                    <FieldError className="mt-0.5 text-xs font-medium text-negative">
                      {error.message}
                    </FieldError>
                  )}
                </TextField>
              )}
            />
            <Controller<ConnectEmailFormState>
              name="verificationCode"
              render={({ field: { ref, disabled, ...field }, fieldState: { invalid, error } }) => (
                <TextField
                  {...field}
                  className="mt-6 flex flex-col"
                  isDisabled={disabled}
                  isInvalid={invalid}
                  isRequired
                  isReadOnly={fetching}
                >
                  <Label className="mb-[5px] font-semibold">Verification Code</Label>
                  <div
                    className={clsx(
                      'flex rounded-sm border pe-4 hover:border-text-4 has-[:focus]:border-text-4',
                      fetching
                        ? 'border-background-2 bg-background-2 text-text-6'
                        : 'border-background-7 bg-background-7',
                    )}
                  >
                    <Input
                      ref={ref}
                      className="flex-1 bg-transparent py-2.5 ps-4 font-medium outline-none"
                    />
                    <SendCodeButton disabled={fetching} />
                  </div>
                  {error && (
                    <FieldError className="mt-0.5 text-xs font-medium text-negative">
                      {error.message}
                    </FieldError>
                  )}
                </TextField>
              )}
            />
            <Button
              variant="contained"
              type="submit"
              className="mx-auto mt-[1.875rem] block w-[6.25rem] py-2.5 text-sm"
              loading={fetching}
              disabled={!isValid}
            >
              {t('btn.verify')}
            </Button>
          </Form>
        </FormProvider>
      </Modal>
    </>
  );
}
